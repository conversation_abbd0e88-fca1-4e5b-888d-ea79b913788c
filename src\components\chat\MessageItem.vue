<template>
  <div
    class="message-item"
    :class="{
      'user-message': message.role === 'user',
      'ai-message': message.role === 'assistant'
    }"
    :data-message-id="message.id"
  >
    <div class="message-avatar" v-if="message.role === 'assistant'">
      <div class="ai-avatar">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
    </div>

    <div class="message-content-wrapper">
      <div v-if="message.role === 'assistant'" class="message-header">
        <span class="sender-name">AI助手</span>
        <div class="message-status" :class="message.status">
          <div v-if="message.status === 'loading'" class="loading-dots">
            <span></span><span></span><span></span>
          </div>
          <span v-else-if="message.status === 'error'" class="error-text">发送失败</span>
        </div>
      </div>

      <div class="message-content" v-html="formattedContent"></div>

      <div class="message-actions" v-if="message.role === 'assistant'">
        <div class="action-buttons">
          <button @click="$emit('copy-message', message.content)" class="action-btn" title="复制到剪贴板">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
          <button @click="$emit('regenerate-message', message.id)" class="action-btn" title="重新生成">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <button class="action-btn" title="URL上传">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="currentColor" stroke-width="2"/>
              <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
        </div>
        <span class="message-time">{{ formatTime(message.timestamp) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import { formatRelativeTime } from '@/utils/common'

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    default: 0
  },
  userAvatar: {
    type: String,
    default: null
  }
})

defineEmits(['copy-message', 'regenerate-message'])

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) {
        console.error('Highlight error:', err)
      }
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

const formattedContent = computed(() => {
  if (!props.message.content) return ''
  
  try {
    // 处理 Markdown 内容
    let html = marked(props.message.content)
    
    // 为代码块添加复制按钮
    html = html.replace(
      /<pre><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g,
      (match, lang, code) => {
        return `
          <div class="code-block">
            <div class="code-header">
              <span class="code-lang">${lang}</span>
              <button class="copy-code-btn" onclick="copyCode(this)">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                </svg>
                复制
              </button>
            </div>
            <pre><code class="language-${lang}">${code}</code></pre>
          </div>
        `
      }
    )
    
    return html
  } catch (error) {
    console.error('Markdown parsing error:', error)
    return props.message.content
  }
})

const formatTime = (timestamp) => {
  return formatRelativeTime(timestamp)
}
</script>

<style lang="scss" scoped>
.message-item {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0;
  max-width: 100%;
  padding: 1rem 1.5rem;
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &.user-message {
    justify-content: flex-end;

    .message-content-wrapper {
      max-width: 70%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1rem 1.25rem;
      border-radius: 20px 20px 4px 20px;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 1px;
        background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
      }

      .message-content {
        :deep(p) {
          color: white;
          margin: 0;
          font-size: 0.95rem;
          line-height: 1.5;
        }

        :deep(code) {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border-radius: 4px;
          padding: 0.125rem 0.25rem;
        }
      }
    }
  }

  &.ai-message {
    justify-content: flex-start;

    .message-avatar {
      flex-shrink: 0;
      margin-top: 0.25rem;

      .ai-avatar {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      }
    }

    .message-content-wrapper {
      flex: 1;
      max-width: calc(100% - 48px);

      .message-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;

        .sender-name {
          font-size: 0.875rem;
          font-weight: 600;
          color: #1f2937;
        }

        .message-status {
          &.loading {
            .loading-dots {
              display: flex;
              gap: 0.25rem;

              span {
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background: #6b7280;
                animation: typing 1.4s ease-in-out infinite both;

                &:nth-child(1) { animation-delay: -0.32s; }
                &:nth-child(2) { animation-delay: -0.16s; }
                &:nth-child(3) { animation-delay: 0s; }
              }
            }
          }

          &.error {
            .error-text {
              font-size: 0.75rem;
              color: #ef4444;
            }
          }
        }
      }
    }
  }

  .message-content {
    line-height: 1.6;
    word-wrap: break-word;
    color: #1f2937;
    font-size: 0.95rem;
    background: #ffffff;
    padding: 1.25rem 1.5rem;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f5f9;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.1) 50%, transparent 100%);
      border-radius: 18px 18px 0 0;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.08);
      border-color: rgba(102, 126, 234, 0.2);
    }

    :deep(p) {
      margin: 0 0 0.75rem 0;
      color: #1f2937;
      line-height: 1.6;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
      color: #111827;
      margin: 1rem 0 0.5rem 0;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }
    }

    :deep(ul), :deep(ol) {
      margin: 0.5rem 0;
      padding-left: 1.5rem;
      color: #374151;
    }

    :deep(li) {
      margin: 0.25rem 0;
    }

    :deep(code) {
      background: #f3f4f6;
      color: #374151;
      padding: 0.125rem 0.375rem;
      border-radius: 4px;
      font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
      font-size: 0.9em;
    }

    :deep(.code-block) {
      margin: 1.25rem 0;
      border-radius: 12px;
      overflow: hidden;
      background: #1e293b;
      border: 1px solid #334155;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      .code-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.875rem 1.25rem;
        background: linear-gradient(135deg, #334155 0%, #475569 100%);
        border-bottom: 1px solid #475569;

        .code-lang {
          font-size: 0.75rem;
          font-weight: 600;
          color: #67e8f9;
          text-transform: uppercase;
          background: rgba(103, 232, 249, 0.1);
          padding: 0.25rem 0.5rem;
          border-radius: 6px;
        }

        .copy-code-btn {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.375rem 0.75rem;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          color: #e2e8f0;
          font-size: 0.75rem;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          }
        }
      }

      pre {
        margin: 0;
        padding: 1.25rem;
        overflow-x: auto;
        background: transparent;

        code {
          background: transparent;
          color: #e2e8f0;
          padding: 0;
          font-size: 0.9rem;
          line-height: 1.6;
          font-family: 'Fira Code', 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
        }
      }
    }

    :deep(blockquote) {
      margin: 1.25rem 0;
      padding: 1rem 1.25rem;
      border-left: 4px solid #667eea;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: 0 12px 12px 0;
      color: #475569;
      position: relative;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);

      &::before {
        content: '"';
        position: absolute;
        top: -0.5rem;
        left: 1rem;
        font-size: 2rem;
        color: #667eea;
        font-weight: bold;
        background: #f8fafc;
        padding: 0 0.25rem;
      }
    }

    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 1rem 0;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #e5e7eb;

      th, td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #f3f4f6;
        color: #374151;
      }

      th {
        background: #f9fafb;
        font-weight: 600;
      }

      tr:last-child td {
        border-bottom: none;
      }
    }
  }

  .message-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.75rem;
    padding: 0.5rem 1.25rem 0;

    .action-buttons {
      display: flex;
      gap: 0.25rem;

      .action-btn {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #64748b;

        &:hover {
          background: #f1f5f9;
          color: #475569;
          border-color: #cbd5e1;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .message-time {
      font-size: 0.75rem;
      color: #94a3b8;
      font-weight: 500;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}
</style>
