<template>
  <div class="messages-container" ref="messagesContainer" @scroll="handleScroll">
    <EmptyState 
      v-if="!hasMessages" 
      :suggested-questions="suggestedQuestions"
      @send-message="$emit('send-message', $event)"
    />
    
    <div v-else class="messages-list">
      <MessageItem
        v-for="(message, index) in messages"
        :key="message.id"
        :message="message"
        :index="index"
        @copy-message="$emit('copy-message', $event)"
        @regenerate-message="$emit('regenerate-message', $event)"
      />
      
      <!-- 加载更多指示器 -->
      <div v-if="loading" class="loading-indicator">
        <div class="loading-avatar">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="loading-content">
          <div class="loading-dots">
            <span></span><span></span><span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'
import EmptyState from './EmptyState.vue'
import MessageItem from './MessageItem.vue'
import { throttle } from 'lodash-es'

const props = defineProps({
  messages: {
    type: Array,
    default: () => []
  },
  hasMessages: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  suggestedQuestions: {
    type: Array,
    default: () => [
      '帮我写一篇关于人工智能的文章',
      '解释一下量子计算的原理',
      '推荐几本值得阅读的书籍',
      '如何提高工作效率？'
    ]
  },
  shouldAutoScroll: {
    type: Boolean,
    default: true
  }
})

defineEmits([
  'send-message',
  'copy-message', 
  'regenerate-message',
  'load-more',
  'scroll-change'
])

const messagesContainer = ref(null)

// 节流处理滚动事件
const handleScroll = throttle((event) => {
  const container = event.target
  const { scrollTop, scrollHeight, clientHeight } = container
  
  // 检查是否接近底部
  const isNearBottom = scrollHeight - scrollTop - clientHeight < 100
  
  // 检查是否接近顶部（用于加载更多历史消息）
  const isNearTop = scrollTop < 100
  
  // 发出滚动变化事件
  emit('scroll-change', {
    isNearBottom,
    isNearTop,
    scrollTop,
    scrollHeight,
    clientHeight
  })
  
  // 如果接近顶部，触发加载更多
  if (isNearTop && !props.loading) {
    emit('load-more')
  }
}, 16) // 60fps

// 滚动到底部
const scrollToBottom = (smooth = false) => {
  if (!props.shouldAutoScroll) return
  
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTo({
        top: messagesContainer.value.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto'
      })
    }
  })
}

// 监听消息变化，自动滚动到底部
watch(() => props.messages.length, () => {
  if (props.shouldAutoScroll) {
    scrollToBottom(true)
  }
}, { flush: 'post' })

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  messagesContainer
})
</script>

<style lang="scss" scoped>
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  scroll-behavior: smooth;
  background: transparent;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    border: 2px solid rgba(241, 245, 249, 0.5);

    &:hover {
      background: #94a3b8;
    }
  }

  .messages-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    min-height: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .loading-indicator {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1.5rem;

    .loading-avatar {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;
      margin-top: 0.25rem;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .loading-content {
      flex: 1;

      .loading-dots {
        display: flex;
        gap: 0.25rem;
        margin-top: 0.5rem;

        span {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          animation: loading-bounce 1.4s ease-in-out infinite both;
          box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);

          &:nth-child(1) { animation-delay: -0.32s; }
          &:nth-child(2) { animation-delay: -0.16s; }
          &:nth-child(3) { animation-delay: 0s; }
        }
      }
    }
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0.8) translateY(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2) translateY(-4px);
    opacity: 1;
  }
}
</style>
