<template>
  <div class="app-layout">
    <!-- 苹果风格顶部导航栏 -->
    <header class="app-header">
      <nav class="nav-container">
        <!-- Logo区域 -->
        <div class="nav-brand">
          <router-link to="/" class="brand-link">
            <div class="brand-icon">
              <div class="ai-logo">
                <div class="logo-circle"></div>
                <div class="logo-spark"></div>
              </div>
            </div>
            <span class="brand-text">AI创作助手</span>
          </router-link>
        </div>

        <!-- 导航菜单 -->
        <div class="nav-menu" :class="{ 'nav-menu-open': mobileMenuOpen }">
          <router-link to="/" class="nav-item" @click="closeMobileMenu">首页</router-link>
          <router-link to="/chat" class="nav-item" @click="closeMobileMenu">AI对话</router-link>
          <router-link to="/drawing" class="nav-item" @click="closeMobileMenu">AI绘画</router-link>
          <router-link to="/gallery" class="nav-item" @click="closeMobileMenu">作品展示</router-link>
          <router-link to="/pricing" class="nav-item pricing-link" @click="closeMobileMenu">
            <span>定价方案</span>
            <div class="pricing-badge">HOT</div>
          </router-link>
        </div>

        <!-- 右侧操作区 -->
        <div class="nav-actions">
          <!-- 管理员导航 -->
          <AdminNavigation v-if="safeIsAdmin" />

          <!-- 搜索按钮 -->
          <button class="action-btn search-btn" @click="toggleSearch">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M21 21L16.514 16.506M19 10.5C19 15.194 15.194 19 10.5 19S2 15.194 2 10.5 5.806 2 10.5 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>

          <!-- 用户菜单 -->
          <div v-if="safeIsLoggedIn" class="user-menu" @click.stop>
            <div class="user-avatar" @click="toggleUserMenu">
              <img
                :src="safeUserAvatar"
                :alt="safeUserName"
                @error="handleAvatarError"
                @load="handleAvatarLoad"
              />
              <div class="user-name">{{ safeUserName }}</div>
              <svg class="dropdown-icon" :class="{ active: userMenuOpen }" width="12" height="12" viewBox="0 0 24 24" fill="none">
                <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>

            <!-- 用户下拉菜单 -->
            <div class="user-dropdown" :class="{ active: userMenuOpen }">
              <div class="user-info">
                <img
                  :src="safeUserAvatar"
                  :alt="safeUserName"
                  class="dropdown-avatar"
                  @error="handleAvatarError"
                  @load="handleAvatarLoad"
                />
                <div class="user-details">
                  <div class="user-name">{{ safeUserName }}</div>
                  <div class="user-role">{{ safeUserRole === 'admin' ? '管理员' : '普通用户' }}</div>
                </div>
              </div>

              <div class="menu-divider"></div>

              <div class="menu-items">
                <button class="menu-item" @click="handleUserCommand('profile')">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  个人资料
                </button>

                <button class="menu-item" @click="handleUserCommand('settings')">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  设置
                </button>

                <div class="menu-divider"></div>

                <button class="menu-item logout-item" @click="handleUserCommand('logout')">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4M16 17l5-5-5-5M21 12H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  退出登录
                </button>
              </div>
            </div>
          </div>

          <!-- 登录/注册按钮 -->
          <div v-else class="auth-buttons">
            <router-link to="/register" class="auth-btn register-btn">
              <span class="btn-text">现在加入</span>
              <span class="btn-badge">简单免费</span>
            </router-link>
            <router-link to="/login" class="auth-btn login-btn">登录</router-link>
          </div>

          <!-- 移动端菜单按钮 -->
          <button class="mobile-menu-btn" @click="toggleMobileMenu">
            <span class="hamburger-line" :class="{ active: mobileMenuOpen }"></span>
            <span class="hamburger-line" :class="{ active: mobileMenuOpen }"></span>
            <span class="hamburger-line" :class="{ active: mobileMenuOpen }"></span>
          </button>
        </div>
      </nav>

      <!-- 搜索覆盖层 -->
      <div class="search-overlay" :class="{ active: searchOpen }" @click="closeSearch">
        <div class="search-container" @click.stop>
          <div class="search-input-wrapper">
            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M21 21L16.514 16.506M19 10.5C19 15.194 15.194 19 10.5 19S2 15.194 2 10.5 5.806 2 10.5 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <input
              ref="searchInput"
              v-model="searchKeyword"
              type="text"
              placeholder="搜索作品、用户..."
              @keyup.enter="handleSearch"
              @keyup.esc="closeSearch"
            />
            <button class="search-close" @click="closeSearch">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="app-main">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import AdminNavigation from '@/components/admin/AdminNavigation.vue'

const router = useRouter()

// 使用响应式引用来安全地初始化 stores
const appStore = ref(null)
const userStore = ref(null)

// 在 onMounted 中初始化 stores
onMounted(async () => {
  try {
    const { useAppStore, useUserStore } = await import('@/stores')
    appStore.value = useAppStore()
    userStore.value = useUserStore()
  } catch (error) {
    console.warn('Layout: 初始化 stores 失败:', error)
  }
})

const searchKeyword = ref('')
const searchOpen = ref(false)
const searchInput = ref(null)
const mobileMenuOpen = ref(false)
const userMenuOpen = ref(false)

// 安全地访问 store 属性
const isCollapsed = computed(() => {
  try {
    return appStore.value?.sidebarCollapsed || false
  } catch (error) {
    console.warn('访问 sidebarCollapsed 失败:', error)
    return false
  }
})

const safeIsLoggedIn = computed(() => {
  try {
    return userStore.value?.isLoggedIn || false
  } catch (error) {
    console.warn('访问 isLoggedIn 失败:', error)
    return false
  }
})

const safeIsAdmin = computed(() => {
  try {
    return userStore.value?.isAdmin || false
  } catch (error) {
    console.warn('访问 isAdmin 失败:', error)
    return false
  }
})

const safeUserName = computed(() => {
  try {
    return userStore.value?.userName || '游客'
  } catch (error) {
    console.warn('访问 userName 失败:', error)
    return '游客'
  }
})

const safeUserAvatar = computed(() => {
  try {
    return userStore?.userAvatar || '/default-avatar.svg'
  } catch (error) {
    console.warn('访问 userAvatar 失败:', error)
    return '/default-avatar.svg'
  }
})

const safeUserRole = computed(() => {
  try {
    return userStore?.userRole || 'user'
  } catch (error) {
    console.warn('访问 userRole 失败:', error)
    return 'user'
  }
})

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/search',
      query: { q: searchKeyword.value.trim() }
    })
    closeSearch()
  }
}

const toggleSearch = () => {
  searchOpen.value = !searchOpen.value
  if (searchOpen.value) {
    nextTick(() => {
      searchInput.value?.focus()
    })
  }
}

const closeSearch = () => {
  searchOpen.value = false
  searchKeyword.value = ''
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
}

const closeUserMenu = () => {
  userMenuOpen.value = false
}

const handleUserCommand = async (command) => {
  closeUserMenu() // 执行命令后关闭菜单
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        if (userStore.value && typeof userStore.value.logout === 'function') {
          await userStore.value.logout()
          router.push('/login')
        }
      } catch (error) {
        console.error('Layout: 登出失败:', error)
        // 即使登出失败，也跳转到登录页
        router.push('/login')
      }
      break
  }
}

// 头像加载错误处理
const handleAvatarError = (event) => {
  console.warn('头像加载失败，使用默认头像')
  // 如果不是默认头像，尝试使用默认头像
  if (!event.target.src.includes('default-avatar.svg')) {
    event.target.src = '/default-avatar.svg'
  } else {
    // 如果默认头像也失败，使用data URL的SVG
    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSIyMCIgZmlsbD0iIzYzNjZmMSIvPgogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTUiIHI9IjYiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTcgMzJDNyAyNi40NzcyIDExLjQ3NzIgMjIgMTcgMjJIMjNDMjguNTIyOCAyMiAzMyAyNi40NzcyIDMzIDMyVjM1QzMzIDM2LjY1NjkgMzEuNjU2OSAzOCAzMCAzOEgxMEM4LjM0MzE1IDM4IDcgMzYuNjU2OSA3IDM1VjMyWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+'
  }
}

// 头像加载成功处理
const handleAvatarLoad = (event) => {
  console.log('头像加载成功:', event.target.src)
}

onMounted(async () => {
  // 安全地初始化 appStore
  try {
    if (appStore.value && typeof appStore.value.initApp === 'function') {
      await appStore.value.initApp()
    }
  } catch (error) {
    console.error('Layout: appStore 初始化失败:', error)
  }

  const handleKeydown = (e) => {
    if (e.key === 'Escape') {
      closeSearch()
      closeMobileMenu()
      closeUserMenu()
    }
  }

  const handleClickOutside = (e) => {
    // 点击外部关闭用户菜单
    if (!e.target.closest('.user-menu')) {
      closeUserMenu()
    }
    // 点击外部关闭移动端菜单
    if (!e.target.closest('.nav-menu') && !e.target.closest('.mobile-menu-btn')) {
      closeMobileMenu()
    }
  }

  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // 安全地清理 appStore
  try {
    if (appStore.value && typeof appStore.value.cleanupApp === 'function') {
      appStore.value.cleanupApp()
    }
  } catch (error) {
    console.error('Layout: appStore 清理失败:', error)
  }
})
</script>

<style lang="scss" scoped>
// 变量已通过Vite配置全局导入，无需手动导入

.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fafafa;
}

.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  height: 64px;

  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .nav-brand {
    .brand-link {
      display: flex;
      align-items: center;
      gap: 12px;
      text-decoration: none;
      color: #1d1d1f;
      font-weight: 600;
      font-size: 18px;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.8;
      }
    }

    .brand-icon {
      position: relative;
      width: 32px;
      height: 32px;
    }

    .ai-logo {
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      .logo-circle {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 12px;
          height: 12px;
          background: white;
          border-radius: 50%;
        }
      }

      .logo-spark {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 8px;
        height: 8px;
        background: #FF9500;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }
    }

    .brand-text {
      background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .nav-menu {
    display: flex;
    align-items: center;
    gap: 32px;

    .nav-item {
      color: #1d1d1f;
      text-decoration: none;
      font-size: 16px;
      font-weight: 400;
      padding: 8px 16px;
      border-radius: 20px;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background: rgba(0, 122, 255, 0.1);
        color: #007AFF;
      }

      &.router-link-active {
        background: #007AFF;
        color: white;
        font-weight: 500;
      }

      &.pricing-link {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        &:hover {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .pricing-badge {
          background: #ff6b6b;
          color: white;
          font-size: 10px;
          font-weight: 700;
          padding: 2px 6px;
          border-radius: 8px;
          animation: pulse 2s infinite;
        }
      }
    }
  }

  .nav-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: transparent;
    color: #1d1d1f;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }
  }

  .user-menu {
    position: relative;

    .user-avatar {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 12px;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: rgba(0, 0, 0, 0.05);

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }

      img {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        object-fit: cover;
        background-color: #f3f4f6;
        border: 1px solid #e5e7eb;
        transition: all 0.2s ease;

        &:hover {
          border-color: #d1d5db;
        }
      }

      .user-name {
        font-size: 14px;
        font-weight: 500;
        color: #1d1d1f;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .dropdown-icon {
        transition: transform 0.2s ease;
        color: #86868b;

        &.active {
          transform: rotate(180deg);
        }
      }
    }

    .user-dropdown {
      position: absolute;
      top: calc(100% + 8px);
      right: 0;
      width: 280px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 0, 0, 0.1);
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.2s ease;
      z-index: 1000;

      &.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .user-info {
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 12px;

        .dropdown-avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          object-fit: cover;
          background-color: #f3f4f6;
          border: 2px solid #e5e7eb;
          transition: all 0.2s ease;
        }

        .user-details {
          flex: 1;

          .user-name {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 2px;
          }

          .user-role {
            font-size: 12px;
            color: #86868b;
            background: rgba(0, 122, 255, 0.1);
            color: #007AFF;
            padding: 2px 8px;
            border-radius: 10px;
            display: inline-block;
          }
        }
      }

      .menu-divider {
        height: 1px;
        background: rgba(0, 0, 0, 0.1);
        margin: 0 16px;
      }

      .menu-items {
        padding: 8px 0;

        .menu-item {
          width: 100%;
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 16px;
          border: none;
          background: none;
          color: #1d1d1f;
          font-size: 14px;
          cursor: pointer;
          transition: background 0.2s ease;
          text-align: left;

          &:hover {
            background: rgba(0, 0, 0, 0.05);
          }

          &.logout-item {
            color: #ff3b30;

            &:hover {
              background: rgba(255, 59, 48, 0.1);
            }
          }

          svg {
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .auth-buttons {
    display: flex;
    gap: 12px;
    align-items: center;

    .auth-btn {
      padding: 8px 20px;
      border-radius: 20px;
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      position: relative;

      &.register-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 10px 24px;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

        .btn-text {
          font-weight: 600;
        }

        .btn-badge {
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          backdrop-filter: blur(10px);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }

      &.login-btn {
        background: rgba(255, 255, 255, 0.1);
        color: $text-color-primary;
        border: 1px solid rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  .mobile-menu-btn {
    display: none;
    flex-direction: column;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;

    .hamburger-line {
      width: 18px;
      height: 2px;
      background: #1d1d1f;
      margin: 2px 0;
      transition: all 0.3s ease;
      border-radius: 1px;

      &.active {
        &:nth-child(1) {
          transform: rotate(45deg) translate(5px, 5px);
        }
        &:nth-child(2) {
          opacity: 0;
        }
        &:nth-child(3) {
          transform: rotate(-45deg) translate(7px, -6px);
        }
      }
    }
  }

  .search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.active {
      opacity: 1;
      visibility: visible;
    }

    .search-container {
      position: absolute;
      top: 80px;
      left: 50%;
      transform: translateX(-50%);
      width: 90%;
      max-width: 600px;
      background: white;
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      transform: translateX(-50%) translateY(-20px);
      transition: transform 0.3s ease;
    }

    &.active .search-container {
      transform: translateX(-50%) translateY(0);
    }

    .search-input-wrapper {
      display: flex;
      align-items: center;
      padding: 20px 24px;
      gap: 16px;

      .search-icon {
        color: #8e8e93;
        flex-shrink: 0;
      }

      input {
        flex: 1;
        border: none;
        outline: none;
        font-size: 18px;
        color: #1d1d1f;
        background: transparent;

        &::placeholder {
          color: #8e8e93;
        }
      }

      .search-close {
        width: 32px;
        height: 32px;
        border: none;
        background: rgba(142, 142, 147, 0.1);
        border-radius: 50%;
        color: #8e8e93;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(142, 142, 147, 0.2);
        }
      }
    }
  }
}

.app-main {
  flex: 1;
  background: #fafafa;
  overflow-y: auto;
  height: calc(100vh - 64px);
  padding-top: 64px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 现代化移动端适配
@media (max-width: 768px) {
  .app-header {
    .nav-container {
      padding: 0 $spacing-md;
    }

    .nav-menu {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(15, 23, 42, 0.95);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: $spacing-lg $spacing-xl;
      gap: $spacing-md;
      transform: translateX(100%);
      opacity: 0;
      visibility: hidden;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 9999;

      &.nav-menu-open {
        transform: translateX(0);
        opacity: 1;
        visibility: visible;
      }

      .nav-item {
        width: 100%;
        max-width: 300px;
        text-align: center;
        padding: $spacing-md $spacing-lg;
        border-radius: 16px;
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: white;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        transform: translateY(20px);
        opacity: 0;
        animation: slideInUp 0.6s ease forwards;

        @for $i from 1 through 5 {
          &:nth-child(#{$i}) {
            animation-delay: #{$i * 0.1}s;
          }
        }

        &:hover {
          background: rgba(255, 255, 255, 0.15);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        &.router-link-active {
          background: $gradient-primary;
          color: white;
        }

        &.pricing-link {
          background: $gradient-premium;

          &:hover {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            transform: translateY(-2px) scale(1.02);
          }
        }
      }

      // 小屏幕优化
      @media (max-height: 600px) {
        gap: $spacing-sm;
        padding: $spacing-md $spacing-lg;

        .nav-link {
          padding: $spacing-sm $spacing-md;
          font-size: 16px;
        }
      }
    }

    .mobile-menu-btn {
      display: flex;
      z-index: 10000;
      position: relative;

      .hamburger-line {
        background: $text-color-primary;
        transition: all 0.3s ease;

        &.active {
          background: white;
        }
      }
    }

    .nav-actions {
      gap: $spacing-sm;

      .search-btn,
      .notification-btn {
        width: 36px;
        height: 36px;
      }
    }

    .search-overlay {
      .search-container {
        width: 95%;
        top: 80px;
        padding: $spacing-lg;

        .search-input {
          font-size: 16px; // 防止iOS缩放
        }
      }
    }

    .user-menu {
      .user-avatar {
        padding: 4px 8px;

        .user-name {
          display: none;
        }

        img {
          width: 32px;
          height: 32px;
        }
      }

      .user-dropdown {
        width: calc(100vw - 32px);
        max-width: 320px;
        right: -8px;

        .user-info {
          padding: $spacing-lg;

          .dropdown-avatar {
            width: 56px;
            height: 56px;
          }
        }

        .menu-item {
          padding: $spacing-md $spacing-lg;
          font-size: 16px;
        }
      }
    }
  }
}

// 超小屏幕优化
@media (max-width: 480px) {
  .app-header {
    .nav-brand {
      .brand-text {
        display: none;
      }

      .brand-logo {
        width: 32px;
        height: 32px;
      }
    }

    .nav-menu {
      padding: $spacing-lg;

      .nav-item {
        padding: $spacing-md $spacing-lg;
        font-size: 16px;
      }
    }

    .user-menu .user-dropdown {
      width: calc(100vw - 16px);
      right: -4px;
    }
  }
}

// 移动端菜单动画
@keyframes slideInUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@media (max-width: 480px) {
  .app-header {
    .nav-brand .brand-text {
      display: none;
    }

    .nav-actions {
      gap: 4px;
    }

    .action-btn {
      width: 36px;
      height: 36px;
    }

    .user-menu {
      .user-avatar {
        padding: 4px 8px;

        img {
          width: 24px;
          height: 24px;
        }
      }

      .user-dropdown {
        width: 240px;
        right: -12px;
      }
    }
  }
}
</style>