import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { showLoginRequiredMessage } from '@/utils/message'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 布局组件
const Layout = () => import('@/components/common/Layout.vue')
const SimpleLayout = () => import('@/components/common/SimpleLayout.vue')

// 页面组件 - 懒加载
const Home = () => import('@/views/HomeView.vue')
const Chat = () => import('@/views/chat/ChatViewAdvanced.vue')
const Drawing = () => import('@/views/create/CreateViewAdvanced.vue')
const Gallery = () => import('@/views/gallery/GalleryViewSimple.vue')
const StyleTest = () => import('@/views/StyleTestView.vue')
const Profile = () => import('@/views/user/ProfileView.vue')
const Settings = () => import('@/views/user/SettingsView.vue')
const Pricing = () => import('@/views/PricingView.vue')
const Login = () => import('@/views/auth/LoginView.vue')
const Register = () => import('@/views/auth/RegisterView.vue')
const Terms = () => import('@/views/legal/TermsView.vue')
const Privacy = () => import('@/views/legal/PrivacyView.vue')
const UsersAdmin = () => import('@/views/admin/UsersView.vue')
const TestSimple = () => import('@/views/TestSimple.vue')
const ApiTest = () => import('@/views/test/ApiTest.vue')
const OpenRouterTest = () => import('@/views/test/OpenRouterTest.vue')
const OpenRouterTestSimple = () => import('@/views/test/OpenRouterTestSimple.vue')
const ApiKeyManager = () => import('@/views/admin/ApiKeyManager.vue')
const ApiKeyManagement = () => import('@/views/admin/ApiKeyManagement.vue')
const ApiKeyRotationDemo = () => import('@/views/demo/ApiKeyRotationDemo.vue')
const ApiKeyBasicTest = () => import('@/views/test/ApiKeyBasicTest.vue')
const SimpleTest = () => import('@/views/test/SimpleTest.vue')
const BasicTest = () => import('@/views/test/BasicTest.vue')
const MinimalTest = () => import('@/views/test/MinimalTest.vue')
const SystemMonitor = () => import('@/views/admin/SystemMonitor.vue')
const SystemSettings = () => import('@/views/admin/SystemSettings.vue')
const SystemLogs = () => import('@/views/admin/SystemLogs.vue')
const ApiKeyBatchTester = () => import('@/views/admin/ApiKeyBatchTester.vue')
const MobileTestView = () => import('@/views/MobileTestView.vue')
const NotFound = () => import('@/views/error/NotFoundView.vue')

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 直接的基础测试路由，不使用布局
    {
      path: '/direct-test',
      name: 'DirectTest',
      component: MinimalTest,
      meta: {
        title: '直接测试',
        requiresAuth: false,
      },
    },
    {
      path: '/login',
      name: 'Login',
      component: Login,
      meta: {
        title: '登录',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/register',
      name: 'Register',
      component: Register,
      meta: {
        title: '注册',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/terms',
      name: 'Terms',
      component: Terms,
      meta: {
        title: '用户协议',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/privacy',
      name: 'Privacy',
      component: Privacy,
      meta: {
        title: '隐私政策',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/',
      component: Layout,
      redirect: '/home',
      children: [
        {
          path: 'home',
          name: 'Home',
          component: Home,
          meta: {
            title: '首页',
            icon: 'House',
            requiresAuth: false,
          },
        },
        {
          path: 'chat',
          name: 'Chat',
          component: Chat,
          meta: {
            title: 'AI聊天',
            icon: 'ChatDotRound',
            requiresAuth: true,
          },
        },
        {
          path: 'drawing',
          name: 'Drawing',
          component: Drawing,
          meta: {
            title: 'AI绘画',
            icon: 'Brush',
            requiresAuth: true,
          },
        },
        {
          path: 'gallery',
          name: 'Gallery',
          component: Gallery,
          meta: {
            title: '作品展示',
            icon: 'Picture',
            requiresAuth: false,
          },
        },
        {
          path: 'pricing',
          name: 'Pricing',
          component: Pricing,
          meta: {
            title: '定价方案',
            icon: 'Money',
            requiresAuth: false,
          },
        },
        {
          path: 'style-test',
          name: 'StyleTest',
          component: StyleTest,
          meta: {
            title: '样式测试',
            icon: 'View',
            requiresAuth: false,
            hideInMenu: true, // 隐藏在菜单中，只用于测试
          },
        },
        {
          path: 'mobile-test',
          name: 'MobileTest',
          component: MobileTestView,
          meta: {
            title: '移动端测试',
            icon: 'Monitor',
            requiresAuth: false,
            hideInMenu: true, // 隐藏在菜单中，只用于测试
          },
        },
        {
          path: 'test',
          name: 'Test',
          component: () => import('@/views/TestView.vue'),
          meta: {
            title: '系统测试',
            icon: 'Setting',
            requiresAuth: false,
          },
        },
        {
          path: 'health',
          name: 'HealthCheck',
          component: () => import('@/views/HealthCheck.vue'),
          meta: {
            title: '健康检查',
            icon: 'Monitor',
            requiresAuth: false,
          },
        },
        {
          path: 'profile',
          name: 'Profile',
          component: Profile,
          meta: {
            title: '个人中心',
            icon: 'User',
            requiresAuth: true,
            hideInMenu: true,
          },
        },
        {
          path: 'settings',
          name: 'Settings',
          component: Settings,
          meta: {
            title: '设置',
            icon: 'Setting',
            requiresAuth: true,
            hideInMenu: true,
          },
        },
        {
          path: 'admin/users',
          name: 'UsersAdmin',
          component: UsersAdmin,
          meta: {
            title: '用户管理',
            icon: 'UserFilled',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'test-simple',
          name: 'TestSimple',
          component: TestSimple,
          meta: {
            title: '简单测试',
            icon: 'Tools',
            requiresAuth: false,
          },
        },
        {
          path: 'api-test',
          name: 'ApiTest',
          component: ApiTest,
          meta: {
            title: 'API测试',
            icon: 'Connection',
            requiresAuth: false,
          },
        },
        {
          path: 'openrouter-test',
          name: 'OpenRouterTest',
          component: OpenRouterTest,
          meta: {
            title: 'OpenRouter测试',
            icon: 'Link',
            requiresAuth: false,
          },
        },
        {
          path: 'openrouter-simple',
          name: 'OpenRouterTestSimple',
          component: OpenRouterTestSimple,
          meta: {
            title: 'OpenRouter简单测试',
            icon: 'Link',
            requiresAuth: false,
          },
        },
        {
          path: 'api-key-manager',
          name: 'ApiKeyManager',
          component: ApiKeyManager,
          meta: {
            title: 'API密钥管理',
            icon: 'Key',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'api-key-management',
          name: 'ApiKeyManagement',
          component: ApiKeyManagement,
          meta: {
            title: 'API密钥管理中心',
            icon: 'Setting',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'api-key-rotation-demo',
          name: 'ApiKeyRotationDemo',
          component: ApiKeyRotationDemo,
          meta: {
            title: 'API密钥轮询演示',
            icon: 'Refresh',
            requiresAuth: false,
          },
        },
        {
          path: 'api-key-basic-test',
          name: 'ApiKeyBasicTest',
          component: ApiKeyBasicTest,
          meta: {
            title: 'API密钥基础测试',
            icon: 'Setting',
            requiresAuth: false,
          },
        },
        {
          path: 'system-monitor',
          name: 'SystemMonitor',
          component: SystemMonitor,
          meta: {
            title: '系统监控',
            icon: 'Monitor',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'system-settings',
          name: 'SystemSettings',
          component: SystemSettings,
          meta: {
            title: '系统设置',
            icon: 'Tools',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'system-logs',
          name: 'SystemLogs',
          component: SystemLogs,
          meta: {
            title: '系统日志',
            icon: 'Document',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'api-key-batch-tester',
          name: 'ApiKeyBatchTester',
          component: ApiKeyBatchTester,
          meta: {
            title: 'API密钥批量检测',
            icon: 'Operation',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'simple-test',
          name: 'SimpleTest',
          component: SimpleTest,
          meta: {
            title: '简单测试',
            icon: 'Check',
            requiresAuth: false,
          },
        },
        {
          path: 'basic-test',
          name: 'BasicTest',
          component: BasicTest,
          meta: {
            title: '基础测试',
            icon: 'Check',
            requiresAuth: false,
          },
        },
      ],
    },
    {
      path: '/404',
      name: 'NotFound',
      component: NotFound,
      meta: {
        title: '页面不存在',
        hideInMenu: true,
      },
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/404',
    },
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  console.log('路由守卫: 导航到', to.path, '需要认证:', to.meta.requiresAuth)

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - AI创作助手平台` : 'AI创作助手平台'

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    const userStore = useUserStore()

    // 安全地检查登录状态
    let isLoggedIn = false
    let userRole = 'user'

    try {
      isLoggedIn = userStore?.isLoggedIn || false
      userRole = userStore?.userRole || 'user'
    } catch (error) {
      console.warn('路由守卫: 访问用户状态失败:', error)
      isLoggedIn = false
      userRole = 'user'
    }

    console.log('路由守卫: 检查登录状态', isLoggedIn)

    if (!isLoggedIn) {
      console.log('路由守卫: 用户未登录，重定向到登录页')
      showLoginRequiredMessage()
      next({
        name: 'Login',
        query: { redirect: to.fullPath },
      })
      return
    }

    // 检查是否需要管理员权限
    if (to.meta.requiresAdmin) {
      console.log('路由守卫: 检查管理员权限', userRole)
      if (userRole !== 'admin') {
        console.log('路由守卫: 非管理员用户访问管理页面，拒绝访问')
        ElMessage({
          message: '您没有权限访问此页面',
          type: 'error',
          duration: 4000,
          showClose: true,
          center: true
        })
        next({ name: 'Home' })
        return
      }
    }
  }

  // 如果已登录用户访问登录页，重定向到首页
  if ((to.name === 'Login' || to.name === 'Register')) {
    const userStore = useUserStore()
    console.log('路由守卫: 访问登录页，当前登录状态', userStore.isLoggedIn)
    if (userStore.isLoggedIn) {
      console.log('路由守卫: 已登录用户访问登录页，重定向到首页')
      next({ name: 'Home' })
      return
    }
  }

  console.log('路由守卫: 允许导航')
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
